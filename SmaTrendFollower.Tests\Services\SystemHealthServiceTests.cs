using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using SmaTrendFollower.Services;
using Alpaca.Markets;
using Xunit;

namespace SmaTrendFollower.Tests.Services;

/// <summary>
/// Comprehensive tests for SystemHealthService
/// Tests health monitoring, status reporting, event handling, and background service functionality
/// </summary>
public class SystemHealthServiceTests : IDisposable
{
    private readonly Mock<IMarketDataService> _mockMarketDataService;
    private readonly Mock<ILiveStateStore> _mockLiveStateStore;
    private readonly Mock<ITradingMetricsService> _mockMetricsService;
    private readonly Mock<ILogger<SystemHealthService>> _mockLogger;
    private readonly SystemHealthService _service;
    private readonly HealthServiceConfig _config;

    public SystemHealthServiceTests()
    {
        _mockMarketDataService = new Mock<IMarketDataService>();
        _mockLiveStateStore = new Mock<ILiveStateStore>();
        _mockMetricsService = new Mock<ITradingMetricsService>();
        _mockLogger = new Mock<ILogger<SystemHealthService>>();
        
        _config = new HealthServiceConfig(
            CheckInterval: TimeSpan.FromSeconds(1),
            MaxMemoryMB: 500,
            MaxThreadCount: 50,
            MaxEventHistory: 100
        );

        _service = new SystemHealthService(
            _mockMarketDataService.Object,
            _mockLiveStateStore.Object,
            _mockMetricsService.Object,
            _mockLogger.Object,
            _config
        );
    }

    [Fact]
    public void GetCurrentStatus_ShouldReturnUnknownInitially()
    {
        // Act
        var status = _service.GetCurrentStatus();

        // Assert
        status.Should().Be(SystemHealthStatus.Unknown);
    }

    [Fact]
    public async Task GetHealthReportAsync_ShouldReturnValidReport()
    {
        // Act
        var report = await _service.GetHealthReportAsync();

        // Assert
        report.Should().NotBeNull();
        report.OverallStatus.Should().Be(SystemHealthStatus.Unknown);
        report.HealthChecks.Should().NotBeNull();
        report.RecentEvents.Should().NotBeNull();
        report.GeneratedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public async Task PerformHealthCheckAsync_WithHealthyServices_ShouldReturnTrue()
    {
        // Arrange
        SetupHealthyMarketDataService();
        SetupHealthyLiveStateStore();
        SetupHealthyTradingMetrics();

        // Act
        var result = await _service.PerformHealthCheckAsync();

        // Assert
        result.Should().BeTrue();
        _service.GetCurrentStatus().Should().Be(SystemHealthStatus.Healthy);
    }

    [Fact]
    public async Task PerformHealthCheckAsync_WithUnhealthyMarketData_ShouldReturnFalse()
    {
        // Arrange
        SetupUnhealthyMarketDataService();
        SetupHealthyLiveStateStore();
        SetupHealthyTradingMetrics();

        // Act
        var result = await _service.PerformHealthCheckAsync();

        // Assert
        result.Should().BeFalse();
        _service.GetCurrentStatus().Should().NotBe(SystemHealthStatus.Healthy);
    }

    [Fact]
    public async Task PerformHealthCheckAsync_WithUnhealthyLiveStateStore_ShouldReturnFalse()
    {
        // Arrange
        SetupHealthyMarketDataService();
        SetupUnhealthyLiveStateStore();
        SetupHealthyTradingMetrics();

        // Act
        var result = await _service.PerformHealthCheckAsync();

        // Assert
        result.Should().BeFalse();
        _service.GetCurrentStatus().Should().NotBe(SystemHealthStatus.Healthy);
    }

    [Fact]
    public async Task HealthStatusChanged_ShouldFireWhenStatusChanges()
    {
        // Arrange
        var statusChangedFired = false;
        SystemHealthStatus? oldStatus = null;
        SystemHealthStatus? newStatus = null;

        _service.HealthStatusChanged += (sender, args) =>
        {
            statusChangedFired = true;
            oldStatus = args.PreviousStatus;
            newStatus = args.NewStatus;
        };

        SetupHealthyMarketDataService();
        SetupHealthyLiveStateStore();
        SetupHealthyTradingMetrics();

        // Act
        await _service.PerformHealthCheckAsync();

        // Assert
        statusChangedFired.Should().BeTrue();
        oldStatus.Should().Be(SystemHealthStatus.Unknown);
        newStatus.Should().Be(SystemHealthStatus.Healthy);
    }

    [Fact]
    public async Task HealthEventOccurred_ShouldFireWhenEventRecorded()
    {
        // Arrange
        var eventFired = false;
        HealthEvent? capturedEvent = null;

        _service.HealthEventOccurred += (sender, args) =>
        {
            eventFired = true;
            capturedEvent = args.HealthEvent;
        };

        SetupUnhealthyMarketDataService();

        // Act
        await _service.PerformHealthCheckAsync();

        // Assert
        eventFired.Should().BeTrue();
        capturedEvent.Should().NotBeNull();
        capturedEvent!.Component.Should().Be("MarketDataService");
        capturedEvent.Severity.Should().Be(HealthEventSeverity.High);
    }

    [Fact]
    public async Task GetHealthReportAsync_AfterHealthCheck_ShouldContainResults()
    {
        // Arrange
        SetupHealthyMarketDataService();
        SetupHealthyLiveStateStore();
        SetupHealthyTradingMetrics();

        // Act
        await _service.PerformHealthCheckAsync();
        var report = await _service.GetHealthReportAsync();

        // Assert
        report.HealthChecks.Should().NotBeEmpty();
        report.HealthChecks.Should().ContainKey("MarketDataService");
        report.HealthChecks.Should().ContainKey("LiveStateStore");
        report.HealthChecks.Should().ContainKey("TradingSystem");
        report.HealthChecks.Should().ContainKey("SystemResources");
        report.HealthChecks.Should().ContainKey("NetworkConnectivity");
    }

    [Fact]
    public async Task GetRecentEvents_ShouldReturnLimitedResults()
    {
        // Arrange
        SetupUnhealthyMarketDataService();
        SetupUnhealthyLiveStateStore();

        // Act
        await _service.PerformHealthCheckAsync();
        var events = _service.GetRecentEvents(5);

        // Assert
        events.Should().NotBeNull();
        events.Should().HaveCountLessOrEqualTo(5);
    }

    [Theory]
    [InlineData(SystemHealthStatus.Healthy)]
    [InlineData(SystemHealthStatus.Degraded)]
    [InlineData(SystemHealthStatus.Unhealthy)]
    [InlineData(SystemHealthStatus.Unknown)]
    public void GetCurrentStatus_ShouldReflectActualStatus(SystemHealthStatus expectedStatus)
    {
        // This test verifies that the status property works correctly
        // In a real scenario, the status would be set by health checks
        var initialStatus = _service.GetCurrentStatus();
        initialStatus.Should().Be(SystemHealthStatus.Unknown);
    }

    [Fact]
    public async Task PerformHealthCheckAsync_ShouldRecordMetrics()
    {
        // Arrange
        SetupHealthyMarketDataService();
        SetupHealthyLiveStateStore();
        SetupHealthyTradingMetrics();

        // Act
        await _service.PerformHealthCheckAsync();

        // Assert
        _mockMetricsService.Verify(
            x => x.RecordSystemMetricAsync(
                It.Is<string>(s => s.StartsWith("health_check_")),
                It.IsAny<decimal>(),
                "status",
                It.IsAny<CancellationToken>()),
            Times.AtLeastOnce);
    }

    [Fact]
    public async Task PerformHealthCheckAsync_WithException_ShouldHandleGracefully()
    {
        // Arrange
        _mockMarketDataService
            .Setup(x => x.GetStockBarsAsync(It.IsAny<string>(), It.IsAny<DateTime>(), It.IsAny<DateTime>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Test exception"));

        // Act & Assert - Should not throw
        var result = await _service.PerformHealthCheckAsync();
        result.Should().BeFalse();
    }

    private void SetupHealthyMarketDataService()
    {
        var mockResponse = new Mock<IPage<IBar>>();
        var mockBars = new List<IBar> { Mock.Of<IBar>() };
        mockResponse.Setup(x => x.Items).Returns(mockBars);

        _mockMarketDataService
            .Setup(x => x.GetStockBarsAsync(It.IsAny<string>(), It.IsAny<DateTime>(), It.IsAny<DateTime>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockResponse.Object);
    }

    private void SetupUnhealthyMarketDataService()
    {
        var mockResponse = new Mock<IPage<IBar>>();
        mockResponse.Setup(x => x.Items).Returns(new List<IBar>());

        _mockMarketDataService
            .Setup(x => x.GetStockBarsAsync(It.IsAny<string>(), It.IsAny<DateTime>(), It.IsAny<DateTime>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockResponse.Object);
    }

    private void SetupHealthyLiveStateStore()
    {
        _mockLiveStateStore
            .Setup(x => x.SetMarketStateAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<TimeSpan>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        _mockLiveStateStore
            .Setup(x => x.GetMarketStateAsync<string>(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((string key, CancellationToken _) => DateTime.UtcNow.ToString());
    }

    private void SetupUnhealthyLiveStateStore()
    {
        _mockLiveStateStore
            .Setup(x => x.SetMarketStateAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<TimeSpan>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Redis connection failed"));
    }

    private void SetupHealthyTradingMetrics()
    {
        var stats = new TradingStatistics(
            TotalTrades: 10,
            ProfitableTrades: 7,
            TotalPnL: 1000m,
            WinRate: 0.7m,
            TotalSignals: 15,
            ExecutedSignals: 12,
            SignalExecutionRate: 0.8m,
            AverageConfidence: 0.85m,
            MaxDrawdown: -100m,
            SharpeRatio: 1.5m,
            AverageWin: 200m,
            AverageLoss: -100m,
            GeneratedAt: DateTime.UtcNow
        );

        _mockMetricsService
            .Setup(x => x.GetTradingStatisticsAsync())
            .ReturnsAsync(stats);

        _mockMetricsService
            .Setup(x => x.GetKPIs())
            .Returns(new Dictionary<string, decimal>());
    }

    [Fact]
    public async Task SystemResourcesCheck_ShouldDetectHighMemoryUsage()
    {
        // Arrange
        var configWithLowLimits = new HealthServiceConfig(
            CheckInterval: TimeSpan.FromSeconds(1),
            MaxMemoryMB: 1, // Very low limit to trigger warning
            MaxThreadCount: 1,
            MaxEventHistory: 100
        );

        var serviceWithLowLimits = new SystemHealthService(
            _mockMarketDataService.Object,
            _mockLiveStateStore.Object,
            _mockMetricsService.Object,
            _mockLogger.Object,
            configWithLowLimits
        );

        // Act
        await serviceWithLowLimits.PerformHealthCheckAsync();
        var report = await serviceWithLowLimits.GetHealthReportAsync();

        // Assert
        report.HealthChecks.Should().ContainKey("SystemResources");
        var resourceCheck = report.HealthChecks["SystemResources"];
        resourceCheck.Status.Should().NotBe(HealthCheckStatus.Healthy);

        serviceWithLowLimits.Dispose();
    }

    [Fact]
    public async Task NetworkConnectivityCheck_ShouldHandleNetworkFailure()
    {
        // This test verifies that network connectivity checks handle failures gracefully
        // The actual network call is made to a real endpoint, so we test the error handling path

        // Act
        await _service.PerformHealthCheckAsync();
        var report = await _service.GetHealthReportAsync();

        // Assert
        report.HealthChecks.Should().ContainKey("NetworkConnectivity");
        var networkCheck = report.HealthChecks["NetworkConnectivity"];
        networkCheck.Should().NotBeNull();
        networkCheck.Name.Should().Be("NetworkConnectivity");
    }

    [Fact]
    public async Task HealthReport_ShouldLimitEventHistory()
    {
        // Arrange - Generate many events
        for (int i = 0; i < 100; i++)
        {
            SetupUnhealthyMarketDataService();
            await _service.PerformHealthCheckAsync();
        }

        // Act
        var report = await _service.GetHealthReportAsync();
        var recentEvents = _service.GetRecentEvents(10);

        // Assert
        report.RecentEvents.Should().HaveCountLessOrEqualTo(50); // Limited by GetHealthReportAsync
        recentEvents.Should().HaveCountLessOrEqualTo(10); // Limited by parameter
    }

    [Fact]
    public async Task TradingSystemCheck_ShouldHandlePoorPerformance()
    {
        // Arrange
        var poorStats = new TradingStatistics(
            TotalTrades: 20,
            ProfitableTrades: 2, // Very low win rate
            TotalPnL: -1000m,
            WinRate: 0.1m,
            TotalSignals: 25,
            ExecutedSignals: 20,
            SignalExecutionRate: 0.8m,
            AverageConfidence: 0.6m,
            MaxDrawdown: -500m,
            SharpeRatio: -0.5m,
            AverageWin: 100m,
            AverageLoss: -150m,
            GeneratedAt: DateTime.UtcNow
        );

        _mockMetricsService
            .Setup(x => x.GetTradingStatisticsAsync())
            .ReturnsAsync(poorStats);

        // Act
        await _service.PerformHealthCheckAsync();
        var report = await _service.GetHealthReportAsync();

        // Assert
        report.HealthChecks.Should().ContainKey("TradingSystem");
        var tradingCheck = report.HealthChecks["TradingSystem"];
        tradingCheck.Status.Should().NotBe(HealthCheckStatus.Healthy);
    }

    [Fact]
    public async Task TradingSystemCheck_ShouldAllowNewSystems()
    {
        // Arrange - New system with few trades
        var newSystemStats = new TradingStatistics(
            TotalTrades: 2, // Very few trades
            ProfitableTrades: 1,
            TotalPnL: 100m,
            WinRate: 0.5m,
            TotalSignals: 5,
            ExecutedSignals: 3,
            SignalExecutionRate: 0.6m,
            AverageConfidence: 0.8m,
            MaxDrawdown: -50m,
            SharpeRatio: 0.5m,
            AverageWin: 150m,
            AverageLoss: -50m,
            GeneratedAt: DateTime.UtcNow
        );

        _mockMetricsService
            .Setup(x => x.GetTradingStatisticsAsync())
            .ReturnsAsync(newSystemStats);

        // Act
        await _service.PerformHealthCheckAsync();
        var report = await _service.GetHealthReportAsync();

        // Assert
        report.HealthChecks.Should().ContainKey("TradingSystem");
        var tradingCheck = report.HealthChecks["TradingSystem"];
        tradingCheck.Status.Should().Be(HealthCheckStatus.Healthy); // Should be healthy for new systems
    }

    [Fact]
    public async Task HealthStatusChanged_ShouldNotFireWhenStatusUnchanged()
    {
        // Arrange
        var statusChangedCount = 0;
        _service.HealthStatusChanged += (sender, args) => statusChangedCount++;

        SetupHealthyMarketDataService();
        SetupHealthyLiveStateStore();
        SetupHealthyTradingMetrics();

        // Act - Perform health check twice
        await _service.PerformHealthCheckAsync();
        await _service.PerformHealthCheckAsync();

        // Assert
        statusChangedCount.Should().Be(1); // Should only fire once when status changes from Unknown to Healthy
    }

    [Fact]
    public async Task PerformHealthCheckAsync_ShouldRecordResponseTimes()
    {
        // Arrange
        SetupHealthyMarketDataService();
        SetupHealthyLiveStateStore();
        SetupHealthyTradingMetrics();

        // Act
        await _service.PerformHealthCheckAsync();

        // Assert
        _mockMetricsService.Verify(
            x => x.RecordSystemMetricAsync(
                It.Is<string>(s => s.StartsWith("health_response_time_")),
                It.IsAny<decimal>(),
                "ms",
                It.IsAny<CancellationToken>()),
            Times.AtLeastOnce);
    }

    [Fact]
    public void GetRecentEvents_WithZeroCount_ShouldReturnEmpty()
    {
        // Act
        var events = _service.GetRecentEvents(0);

        // Assert
        events.Should().NotBeNull();
        events.Should().BeEmpty();
    }

    [Fact]
    public async Task HealthCheck_WithMixedResults_ShouldReturnDegraded()
    {
        // Arrange - Mix of healthy and unhealthy services
        SetupHealthyMarketDataService();
        SetupUnhealthyLiveStateStore(); // One unhealthy service
        SetupHealthyTradingMetrics();

        // Act
        await _service.PerformHealthCheckAsync();

        // Assert
        var status = _service.GetCurrentStatus();
        status.Should().BeOneOf(SystemHealthStatus.Degraded, SystemHealthStatus.Unhealthy);
    }

    public void Dispose()
    {
        _service?.Dispose();
    }
}
